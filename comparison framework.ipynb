import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
import re
import matplotlib.pyplot as plt
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
import warnings
warnings.filterwarnings("ignore")
import math
import os
from datetime import date, timedelta, datetime
import time
from tqdm import tqdm
import pyodbc
import seaborn as sns
from scipy import stats
import xlsxwriter
from matplotlib.ticker import MaxNLocator
from matplotlib.backends.backend_pdf import PdfPages
import itertools
from data import Data
d = Data()
import numpy_financial as npf
from scipy.optimize import newton, brentq
start_time = time.perf_counter()

# sheet1 = pd.read_excel('Scheme Names - Traditional SIP.xlsx', sheet_name='Sheet1')

aum_sheet = pd.read_excel('Scheme Names - Traditional SIP.xlsx', sheet_name='AUM')
aum_sheet = aum_sheet.iloc[33:, 1:]
aum_sheet.columns = aum_sheet.iloc[1]
aum_sheet = aum_sheet.iloc[2:, :]
aum_sheet = aum_sheet.dropna(how='all', axis=1)
aum_sheet = aum_sheet.dropna(how='all', axis=0)
aum_sheet.index = aum_sheet.iloc[:, 0]
aum_sheet = aum_sheet.iloc[:, 1:]
aum_sheet = aum_sheet.apply(pd.to_numeric, errors='coerce')
aum_sheet.index.name = 'Date'
aum_sheet.index = pd.to_datetime(aum_sheet.index) 
aum_sheet.head()


vintage_sheet = pd.read_excel('Scheme Names - Traditional SIP.xlsx', sheet_name='Vintage')
vintage_sheet = vintage_sheet.iloc[32:, 1:]
vintage_sheet.columns = vintage_sheet.iloc[1]
vintage_sheet = vintage_sheet.iloc[2:, :]
vintage_sheet = vintage_sheet.dropna(how='all', axis=1)
vintage_sheet = vintage_sheet.dropna(how='all', axis=0)
vintage_sheet.index = vintage_sheet.iloc[:, 0]
vintage_sheet = vintage_sheet.iloc[:, 1:]
vintage_sheet = vintage_sheet.apply(pd.to_numeric, errors='coerce')
vintage_sheet.index.name = 'Date'
vintage_sheet.index = pd.to_datetime(vintage_sheet.index)
vintage_sheet.head()

aum_sheet = pd.read_excel('Scheme Names - Traditional SIP.xlsx', sheet_name='AUM')
vintage_sheet = pd.read_excel('Scheme Names - Traditional SIP.xlsx', sheet_name='Vintage')

aum_fund_list = aum_sheet.iloc[1:32, :]
aum_fund_list.columns = aum_fund_list.iloc[0]
aum_fund_list = aum_fund_list.iloc[1:, :]
aum_fund_list = aum_fund_list.dropna(how='all', axis=1)
aum_fund_list = aum_fund_list.dropna(how='all', axis=0)
aum_fund_list = aum_fund_list.reset_index(drop=True)
aum_fund_list = aum_fund_list[['Scheme Name', 'Category']]
aum_fund_list
aum_fund_list = aum_fund_list.groupby('Category')['Scheme Name'].apply(list).to_dict()
aum_fund_list = {f'AUM_' + key + '_Portfolio':value for key, value in aum_fund_list.items()}
aum_fund_list

vintage_fund_list = vintage_sheet.iloc[0:32, :]
vintage_fund_list.columns = vintage_fund_list.iloc[0]
vintage_fund_list = vintage_fund_list.iloc[1:, :]
vintage_fund_list = vintage_fund_list.dropna(how='all', axis=1)
vintage_fund_list = vintage_fund_list.dropna(how='all', axis=0)
vintage_fund_list = vintage_fund_list.reset_index(drop=True)
vintage_fund_list = vintage_fund_list[['Scheme Name', 'Category']]
vintage_fund_list = vintage_fund_list.groupby('Category')['Scheme Name'].apply(list).to_dict()
vintage_fund_list = {f'Vintage_' + key + '_Portfolio':value for key, value in vintage_fund_list.items()}
vintage_fund_list





